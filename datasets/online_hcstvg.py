
import os
import json
from copy import deepcopy
import torch
import random
from tqdm import tqdm
import torch.utils.data as data
import numpy as np

from utils.bounding_box import BoxList
from .hcstvg import HCSTVGDataset  # 从现有的 hcstvg.py 中导入基类

class OnlineHCSTVGDataset(HCSTVGDataset):
    """
    HC-STVG数据集的在线流式处理版本。

    这个类继承自HCSTVGDataset，但将其改造为模拟流式视频输入。
    它将每个视频切分成多个重叠的窗口（数据块），模型在每次迭代中只处理一个数据块。
    """

    def __init__(self, cfg, split, transforms=None):
        """
        初始化在线数据集。

        Args:
            cfg: 配置对象。
            split (str): 'train' 或 'test'。
            transforms: 应用于数据的数据增强。
        """
        # --- 固定大小滑动窗口相关参数 ---
        # 窗口大小（每个窗口包含的帧数，默认8帧）
        self.window_size = cfg.INPUT.get('WINDOW_SIZE', 8)
        
        # 滑动步长（默认1帧）
        self.slide_stride = cfg.INPUT.get('SLIDE_STRIDE', 1)
        
        # 是否使用零填充处理边界情况
        self.use_zero_padding = cfg.INPUT.get('USE_ZERO_PADDING', True)

        # 调用父类的构造函数来加载所有原始数据
        super().__init__(cfg, split, transforms)
        
        # 在多GPU环境下，确保数据分配的一致性
        from utils.comm import get_world_size, get_rank
        world_size = get_world_size()
        rank = get_rank()
        
        print(f"=== 数据集分配信息 ===")
        print(f"进程 {rank}/{world_size}")
        print(f"原始数据集大小: {len(self.all_gt_data)}")
        
        # 先用部分视频验证系统
        self.all_gt_data = self.all_gt_data[:10]  # 先用10个视频验证
        print(f"验证数据集大小: {len(self.all_gt_data)}")
        
        # 确保每个GPU处理不同的视频
        if world_size > 1:
            # 将视频按GPU数量分组，确保每个GPU处理完整的视频
            videos_per_gpu = len(self.all_gt_data) // world_size
            start_idx = rank * videos_per_gpu
            if rank == world_size - 1:  # 最后一个GPU处理剩余的视频
                end_idx = len(self.all_gt_data)
            else:
                end_idx = start_idx + videos_per_gpu
            
            assigned_videos = self.all_gt_data[start_idx:end_idx]
            video_ids = [v['item_id'] for v in assigned_videos]
            
            print(f"GPU {rank}: 分配 {len(assigned_videos)} 个视频（索引 {start_idx}-{end_idx-1}）")
            print(f"GPU {rank}: 视频ID: {video_ids}")
            
            self.all_gt_data = assigned_videos
        else:
            print(f"单GPU模式: 处理所有 {len(self.all_gt_data)} 个视频")
        
        # 将加载的完整视频数据切分成固定大小的滑动窗口
        self.window_infos = self.create_sliding_windows(self.all_gt_data)
        
        print(f"GPU {rank}: 生成 {len(self.window_infos)} 个窗口")
        print("=" * 50)

    def create_sliding_windows(self, all_gt_data):
        """
        创建固定大小的滑动窗口。
        每个窗口大小为window_size帧，以slide_stride步长滑动。
        """
        for target_frame_idx in range(0, video_length, self.slide_stride):
            # 计算当前窗口的起始帧索引
            window_start = target_frame_idx - (self.window_size - 1)
            window_end = target_frame_idx + 1  # 不包含end
            
            window_info = {
                'anno_idx': anno_idx,           # 原始视频在 all_gt_data 中的索引
                'window_start': window_start,   # 窗口起始帧索引（可能为负数）
                'window_end': window_end,       # 窗口结束帧索引（不包含）
                'target_frame_idx': target_frame_idx,  # 目标预测帧索引
                'video_length': video_length    # 视频总长度
            }
            window_infos.append(window_info)
        
        print(f"成功生成 {len(window_infos)} 个滑动窗口。")
        return window_infos

    def __len__(self):
        """返回数据集中窗口（数据块）的总数。"""
        return len(self.window_infos)

    def __getitem__(self, index: int):
        """
        获取一个固定大小的滑动窗口数据。
        返回window_size帧的数据，但只关注最后一帧的预测结果。
        """
        # 1. 根据索引获取窗口信息
        window_info = self.window_infos[index]
        anno_idx = window_info['anno_idx']
        window_start = window_info['window_start']
        window_end = window_info['window_end']
        target_frame_idx = window_info['target_frame_idx']
        video_length = window_info['video_length']
        
        # 2. 获取对应的原始视频标注信息
        video_data = deepcopy(self.all_gt_data[anno_idx])
        
        # 3. 构建固定大小的窗口帧序列
        window_frame_indices = []
        window_frame_ids = []
        
        for frame_idx in range(window_start, window_end):
            if frame_idx < 0:
                # 零填充：使用负数表示填充帧，实际数据使用第0帧
                window_frame_indices.append(0)
                window_frame_ids.append(frame_idx)  # 使用负数标识填充帧
            elif frame_idx >= video_length:
                # 超出范围：使用最后一帧
                window_frame_indices.append(video_length - 1)
                window_frame_ids.append(video_data['frame_ids'][-1])  # 使用最后一帧的frame_id
            else:
                # 正常范围内的帧
                window_frame_indices.append(frame_idx)
                window_frame_ids.append(video_data['frame_ids'][frame_idx])
        
        # 确保窗口大小正确
        assert len(window_frame_indices) == self.window_size
        assert len(window_frame_ids) == self.window_size
        
        # 4. 构建数据项
        data_item = {}
        data_item['frame_ids'] = window_frame_ids
        data_item['vid'] = video_data['vid']
        data_item['width'] = video_data['width']
        data_item['height'] = video_data['height']
        data_item['description'] = video_data['description']
        data_item['gt_temp_bound'] = video_data['gt_temp_bound']
        
        # 提取窗口对应的actioness和heatmap（使用零填充处理边界）
        actioness_window = []
        start_heatmap_window = []
        end_heatmap_window = []
        
        for frame_idx in window_frame_indices:
            # 使用有效的帧索引获取对应的标注
            valid_idx = max(0, min(frame_idx, len(video_data['actioness']) - 1))
            actioness_window.append(video_data['actioness'][valid_idx])
            start_heatmap_window.append(video_data['start_heatmap'][valid_idx])
            end_heatmap_window.append(video_data['end_heatmap'][valid_idx])
        
        data_item['actioness'] = np.array(actioness_window)
        data_item['start_heatmap'] = np.array(start_heatmap_window)
        data_item['end_heatmap'] = np.array(end_heatmap_window)
        
        # 5. 加载窗口的视频帧
        frames = self.load_frames(data_item)
        
        # 6. 处理边界框 - 只关注目标帧（窗口的最后一帧）
        target_actioness = data_item['actioness'][-1]  # 目标帧的actioness
        
        if target_actioness > 0 and target_frame_idx < video_length:
            # 目标帧是正样本且在有效范围内
            temp_gt_start, _ = data_item['gt_temp_bound']
            bbox_idx = target_frame_idx - temp_gt_start
            
            if 0 <= bbox_idx < len(video_data['bboxs']):
                bbox = video_data['bboxs'][bbox_idx:bbox_idx+1]  # 只取目标帧的bbox
                bboxs_tensor = torch.from_numpy(bbox).reshape(-1, 4)
            else:
                bboxs_tensor = torch.empty((0, 4))
        else:
            # 目标帧是负样本
            bboxs_tensor = torch.empty((0, 4))
        
        w, h = data_item['width'], data_item['height']
        bboxs = BoxList(bboxs_tensor, (w, h), 'xyxy')
        
        sentence = data_item['description'].lower()
        input_dict = {'frames': frames, 'boxs': bboxs, 'text': sentence, 'actioness': data_item['actioness']}
        
        # 7. 应用数据增强
        if self.transforms is not None:
            input_dict = self.transforms(input_dict)
        
        # 8. 准备目标字典
        targets = {
            'item_id': video_data['item_id'],
            'frame_ids': data_item['frame_ids'],
            'actioness': torch.from_numpy(data_item['actioness']),
            'start_heatmap': torch.from_numpy(data_item['start_heatmap']),
            'end_heatmap': torch.from_numpy(data_item['end_heatmap']),
            'boxs': input_dict['boxs'],
            'img_size': input_dict['frames'].shape[2:],
            'ori_size': torch.tensor([h, w], dtype=torch.float32),
            # 滑动窗口相关信息
            'target_frame_idx': target_frame_idx,  # 目标预测帧的索引
            'window_start': window_start,          # 窗口起始帧索引
            'window_size': self.window_size        # 窗口大小
        }
        
        return input_dict['frames'], sentence, targets
