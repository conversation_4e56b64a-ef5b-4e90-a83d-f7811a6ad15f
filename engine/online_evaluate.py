import pdb
import torch
import torch.nn
from tqdm import tqdm
import numpy as numpy_lib
from typing import List, Dict, Tuple

from utils.comm import synchronize, is_main_process, get_rank, get_world_size
from utils.misc import to_device
from utils.logger import create_logging_tqdm
from utils.gpu_monitor import log_gpu_memory, check_gpu_assignment

@torch.no_grad()
def aggregate_sliding_window_predictions(video_predictions_cache, post_processor):
    """
    聚合滑动窗口的预测结果。
    每个窗口只取最后一帧的预测结果，按时间顺序拼接成完整视频序列。
    
    Args:
        video_predictions_cache: 所有窗口的预测结果
        post_processor: 后处理器
    Returns:
        所有窗口拼接好的最终预测结果 
    """
    full_video_predictions = {}
    
    for video_id, window_predictions in video_predictions_cache.items():
        print(f"🔧 聚合视频 {video_id} 的 {len(window_predictions)} 个滑动窗口预测...")
        
        # 收集所有目标帧的预测信息
        frame_predictions = {}  # target_frame_idx -> {'sted_logits': tensor, 'boxes': tensor}
        ori_size = None
        
        # 第一步：收集每个窗口的目标帧预测
        for window_pred in window_predictions:
            # 每个窗口的预测结果
            sted_logits = window_pred['sted_logits']      # [window_size, 2]
            boxes = window_pred['boxes']                  # [window_size, 4]
            target_frame_idx = window_pred['target_frame_idx']  # 目标帧索引
            ori_size = window_pred['ori_size']
            
            # 只取窗口的最后一帧预测（即目标帧）
            target_sted = sted_logits[-1]  # [2] - 目标帧的时序预测
            target_box = boxes[-1]         # [4] - 目标帧的边界框预测
            
            # 只处理有效的目标帧（非负数帧ID）
            if target_frame_idx >= 0:
                frame_predictions[target_frame_idx] = {
                    'sted_logits': target_sted,
                    'boxes': target_box
                }
        
        if not frame_predictions:
            print(f"⚠️ 视频 {video_id} 没有有效的帧预测")
            continue
        
        # 第二步：按帧索引排序，构建连续的预测序列
        sorted_frame_indices = sorted(frame_predictions.keys())
        min_frame_idx = min(sorted_frame_indices)  # 应该是0
        max_frame_idx = max(sorted_frame_indices)
        total_frames = max_frame_idx - min_frame_idx + 1
        
        print(f"   目标帧范围: {min_frame_idx} - {max_frame_idx}, 共 {total_frames} 帧")
        
        # 构建连续的预测序列
        full_sted_logits = []
        full_boxes = []
        frame_sequence = []
        
        for frame_idx in range(min_frame_idx, max_frame_idx + 1):
            frame_sequence.append(frame_idx)
            
            if frame_idx in frame_predictions:
                pred = frame_predictions[frame_idx]
                full_sted_logits.append(pred['sted_logits'])
                full_boxes.append(pred['boxes'])
            else:
                # 对于缺失的帧，使用零填充
                # 这里简单使用零预测
                device = list(frame_predictions.values())[0]['sted_logits'].device
                zero_sted = torch.zeros(2, device=device)
                zero_box = torch.zeros(4, device=device)
                full_sted_logits.append(zero_sted)
                full_boxes.append(zero_box)
        
        # 转换为tensor格式，添加batch维度
        full_sted_tensor = torch.stack(full_sted_logits).unsqueeze(0)  # [1, total_frames, 2]
        full_boxes_tensor = torch.stack(full_boxes).unsqueeze(0)       # [1, total_frames, 4]
        
        # 第三步：使用PostProcess进行标准处理
        outputs = {
            "pred_sted": full_sted_tensor,  # [1, T, 2]
            "pred_boxes": full_boxes_tensor # [1, T, 4]
        }
        
        # 准备PostProcess需要的参数
        target_sizes = ori_size.unsqueeze(0)  # [1, 2]
        frames_id = [frame_sequence]          # List[List[int]]
        durations = [total_frames]            # List[int]
        
        # 调用PostProcess
        with torch.no_grad():
            pred_boxes_processed, pred_steds_processed = post_processor(
                outputs, target_sizes, frames_id, durations
            )
        
        # 第四步：提取处理结果
        processed_boxes = pred_boxes_processed[0]  # [T, 4]
        processed_sted = pred_steds_processed[0]   # [start_frame, end_frame]
        
        # 构建边界框字典
        boxes_dict = {}
        for i in range(total_frames):
            boxes_dict[i] = [processed_boxes[i].cpu().numpy().tolist()]
        
        # 构建最终结果
        full_video_predictions[video_id] = {
            'temporal_range': processed_sted,
            'boxes': boxes_dict,
            'num_frames': total_frames,
            'sted_logits': full_sted_tensor.squeeze(0),
            'raw_boxes': full_boxes_tensor.squeeze(0)
        }
        
        print(f"   ✅ 完成聚合: 时序范围 {processed_sted}, 总帧数 {total_frames}")
    
    return full_video_predictions

@torch.no_grad()
def do_online_eval(cfg, mode, logger, model, postprocessor, data_loader, evaluator, device):
    """
    Online Video Spatial-Temporal Grounding Evaluation
    每个GPU独立处理分配的视频，计算各自的指标
    """
    model.eval()
    
    # 检查GPU分配
    check_gpu_assignment()
    log_gpu_memory(logger, "评估开始前")
    
    logger.info("Start online datasets evaluation on the {} split of {} dataset".format(mode, cfg.DATASET.NAME))
    logger.info(f"🎯 开始在线评估 - 设备: {device}, 数据集: {cfg.DATASET.NAME}")

    # 用于存储当前GPU处理的视频预测结果
    video_predictions_cache = {}
    total_windows = 0
    
    # 使用标准的tqdm进度条
    progress_bar = tqdm(data_loader, desc=f"GPU{get_rank()}处理视频窗口")
    
    for iteration, (images, targets) in enumerate(progress_bar):
        images = to_device(images, device)
        targets = [to_device(target, device) for target in targets]
        
        # 前向推理
        with torch.no_grad():
            outputs = model(images, targets)
        
        # 后处理
        pred_sted_logits = outputs['pred_sted_logits']  # [B, T, 2]
        pred_boxes = outputs['pred_boxes']  # [B, T, 4] 或 [T, 4]
        
        total_windows += len(targets)

        # 收集预测结果
        for i in range(len(targets)):
            video_id = targets[i]['item_id']
            if video_id not in video_predictions_cache:
                video_predictions_cache[video_id] = []
            
            # 对于滑动窗口，我们需要记录目标帧索引
            target_frame_idx = targets[i]['target_frame_idx']
            
            # 处理 pred_boxes 的形状问题
            if pred_boxes.dim() == 2:  # [T_window, 4] 情况
                window_boxes = pred_boxes
            else:  # [B, T_window, 4] 情况 
                window_boxes = pred_boxes[i]
            
            # 存储该窗口的预测结果
            video_predictions_cache[video_id].append({
                'sted_logits': pred_sted_logits[i].cpu(),    # [window_size, 2]
                'boxes': window_boxes.cpu(),                 # [window_size, 4]
                'target_frame_idx': target_frame_idx,        # 目标帧索引
                'ori_size': targets[i]['ori_size'].cpu()
            })
            
            # 添加多GPU验证日志
            if len(video_predictions_cache[video_id]) == 1:
                logger.info(f"进程 {get_rank()} 开始处理视频 {video_id}")

    # 每个GPU独立聚合和评估自己的视频
    if video_predictions_cache:
        logger.info(f"进程 {get_rank()} 开始聚合 {len(video_predictions_cache)} 个视频...")
        
        # 使用滑动窗口聚合策略
        full_video_preds = aggregate_sliding_window_predictions(video_predictions_cache, postprocessor)
        
        if full_video_preds:
            logger.info(f"✅ 进程 {get_rank()} 聚合成功，共{len(full_video_preds)}个视频")
            
            # 构建评估器需要的格式
            final_temp_preds = {}
            final_bbox_preds = {}
            
            for video_id, pred_data in full_video_preds.items():
                # 时序预测 - 参考evaluate.py的格式
                final_temp_preds[video_id] = {'sted': pred_data['temporal_range']}
                
                # 边界框预测 - 已经是evaluate.py兼容的格式（每个框包装在列表中）
                final_bbox_preds[video_id] = pred_data['boxes']
                
                # 输出详细信息
                temporal_range = pred_data['temporal_range']
                logger.info(f"   进程 {get_rank()} 视频 {video_id}: 时序 [{temporal_range[0]}-{temporal_range[1]}], "
                           f"帧数 {len(pred_data['boxes'])}")
            
            # 每个GPU独立计算自己处理的视频指标
            evaluator.update(final_bbox_preds)
            evaluator.video_update(final_temp_preds)
            
            # 不调用同步，每个GPU独立计算指标
            gpu_results = evaluator.summarize_without_sync()
            
            # 汇总处理统计
            total_videos = len(video_predictions_cache)
            logger.info(f"📊 进程 {get_rank()} 处理完成: {total_videos}个视频, {total_windows}个窗口")
            
            return gpu_results
    
    # 在处理完成后记录内存使用
    log_gpu_memory(logger, "评估完成后")
    
    return None
