import logging
import os
import sys
from tqdm import tqdm

class TqdmLoggingHandler(logging.Handler):
    """自定义Handler，让tqdm进度条也能记录到日志文件"""
    def __init__(self, level=logging.NOTSET):
        super().__init__(level)

    def emit(self, record):
        try:
            msg = self.format(record)
            tqdm.write(msg)
        except Exception:
            self.handleError(record)

class LoggingTqdm(tqdm):
    """增强的tqdm，支持同时输出到日志文件"""
    def __init__(self, *args, logger=None, **kwargs):
        self.logger = logger
        self._last_logged_n = 0
        super().__init__(*args, **kwargs)

    def display(self, msg=None, pos=None):
        # 调用父类方法显示到终端
        super().display(msg, pos)
        
        # 记录进度到日志 (每10%记录一次，避免日志过多)
        if self.logger and self.total:
            progress_percent = (self.n / self.total) * 100
            
            # 每处理10%或最后一个项目时记录
            if (self.n - self._last_logged_n >= self.total * 0.1) or (self.n == self.total):
                clean_desc = self.desc or "处理"
                self.logger.info(f"进度更新: {clean_desc} {self.n}/{self.total} ({progress_percent:.1f}%)")
                self._last_logged_n = self.n

def setup_logger(name, save_dir, distributed_rank, filename="log.txt"):
    logger = logging.getLogger(name)
    logger.setLevel(logging.DEBUG)
    # don't log results for the non-master process
    if distributed_rank > 0:
        return logger
    
    # 清除已有的handlers
    logger.handlers.clear()
    
    ch = logging.StreamHandler(stream=sys.stdout)
    ch.setLevel(logging.DEBUG)
    formatter = logging.Formatter("%(asctime)s %(name)s %(levelname)s: %(message)s")
    ch.setFormatter(formatter)
    logger.addHandler(ch)

    if save_dir:
        fh = logging.FileHandler(os.path.join(save_dir, filename), encoding='utf-8')
        fh.setLevel(logging.DEBUG)
        fh.setFormatter(formatter)
        logger.addHandler(fh)

    return logger

def create_logging_tqdm(iterable, desc="Processing", logger=None, **kwargs):
    """创建带日志记录功能的进度条"""
    return LoggingTqdm(iterable, desc=desc, logger=logger, **kwargs)
