import torch
import os
from utils.comm import get_rank

def log_gpu_memory(logger, prefix=""):
    """记录当前GPU内存使用情况"""
    if torch.cuda.is_available():
        rank = get_rank()
        device = torch.cuda.current_device()
        allocated = torch.cuda.memory_allocated(device) / 1024**3  # GB
        reserved = torch.cuda.memory_reserved(device) / 1024**3   # GB
        max_allocated = torch.cuda.max_memory_allocated(device) / 1024**3  # GB
        
        logger.info(f"{prefix} GPU {rank} 内存: "
                   f"已分配={allocated:.2f}GB, "
                   f"已保留={reserved:.2f}GB, "
                   f"峰值={max_allocated:.2f}GB")

def check_gpu_assignment():
    """检查GPU分配是否正确"""
    rank = get_rank()
    local_rank = int(os.environ.get('LOCAL_RANK', 0))
    
    print(f"进程 {rank} 环境变量检查:")
    print(f"  LOCAL_RANK: {local_rank}")
    print(f"  CUDA_VISIBLE_DEVICES: {os.environ.get('CUDA_VISIBLE_DEVICES', 'Not set')}")
    print(f"  torch.cuda.current_device(): {torch.cuda.current_device()}")
    print(f"  torch.cuda.device_count(): {torch.cuda.device_count()}")
    
    if torch.cuda.is_available():
        device_name = torch.cuda.get_device_name(torch.cuda.current_device())
        print(f"  当前GPU: {device_name}")