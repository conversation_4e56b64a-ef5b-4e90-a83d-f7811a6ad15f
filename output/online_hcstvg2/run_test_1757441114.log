Running HC-STVG2
  MODE: test
  GPUs (physical ids): 0,1,2,3
  nproc_per_node: 4
  CONFIG: experiments/online_hcstvg2.yaml
  RESOLUTION: 420
  OUTPUT_DIR: output/online_hcstvg2
Launching testing...
W0909 18:05:16.480875 136905945012032 torch/distributed/run.py:757] 
W0909 18:05:16.480875 136905945012032 torch/distributed/run.py:757] *****************************************
W0909 18:05:16.480875 136905945012032 torch/distributed/run.py:757] Setting OMP_NUM_THREADS environment variable for each process to be 1 in default, to avoid your system being overloaded, please further tune the variable for optimal performance in your application as needed. 
W0909 18:05:16.480875 136905945012032 torch/distributed/run.py:757] *****************************************
[W Utils.hpp:135] Warning: Environment variable NCCL_ASYNC_ERROR_HANDLING is deprecated; use TORCH_NCCL_ASYNC_ERROR_HANDLING instead (function getCvarInt)
[W Utils.hpp:135] Warning: Environment variable NCCL_ASYNC_ERROR_HANDLING is deprecated; use TORCH_NCCL_ASYNC_ERROR_HANDLING instead (function getCvarInt)
[W Utils.hpp:135] Warning: Environment variable NCCL_ASYNC_ERROR_HANDLING is deprecated; use TORCH_NCCL_ASYNC_ERROR_HANDLING instead (function getCvarInt)
[W Utils.hpp:135] Warning: Environment variable NCCL_ASYNC_ERROR_HANDLING is deprecated; use TORCH_NCCL_ASYNC_ERROR_HANDLING instead (function getCvarInt)
2025-09-09 18:05:22,837 Video Grounding INFO: Using 4 GPUs
2025-09-09 18:05:22,837 Video Grounding INFO: DATALOADER:
  ASPECT_RATIO_GROUPING: False
  NUM_WORKERS: 8
  SIZE_DIVISIBILITY: 0
DATASET:
  MIN_GT_FRAME: 4
  NAME: Online-HC-STVG
  NUM_CLIP_FRAMES: 32
DATA_DIR: data/hc-stvg2/
DATA_TRUNK: None
FROM_SCRATCH: True
GLOVE_DIR: 
INPUT:
  AUG_SCALE: True
  AUG_TRANSLATE: False
  FLIP_PROB_TRAIN: 0.7
  MAX_QUERY_LEN: 26
  MAX_VIDEO_LEN: 200
  PIXEL_MEAN: [0.485, 0.456, 0.406]
  PIXEL_STD: [0.229, 0.224, 0.225]
  RESOLUTION: 420
  SAMPLE_FPS: 3.2
  SLIDE_STRIDE: 1
  TEMP_CROP_PROB: 0.5
  TRAIN_SAMPLE_NUM: 64
  USE_ZERO_PADDING: True
  WINDOW_SIZE: 8
MODEL:
  CG:
    CONV_LAYERS: 4
    DEC_LAYERS: 6
    DROPOUT: 0.1
    ENC_LAYERS: 6
    FFN_DIM: 2048
    FROM_SCRATCH: True
    HEADS: 8
    HIDDEN: 256
    KERNAL_SIZE: 9
    MAX_MAP_SIZE: 128
    POOLING_COUNTS: [15, 8, 8, 8]
    QUERY_DIM: 4
    SPAT_GT_THETA: 0.8
    SPAT_THETA: 0.8
    TEMP_HEAD: attn
    TEMP_PRED_LAYERS: 6
    TEMP_THETA: 0.5
    USE_ACTION: True
    USE_LEARN_TIME_EMBED: False
  DEVICE: cuda
  DOWN_RATIO: 4
  EMA: True
  EMA_DECAY: 0.9998
  LSTM:
    BIDIRECTIONAL: True
    DROPOUT: 0
    HIDDEN_SIZE: 512
    NAME: lstm
  LSTM_NUM_LAYERS: 2
  QUERY_NUM: 1
  TEXT_MODEL:
    FREEZE: False
    NAME: roberta-base
  USE_LSTM: False
  VISION_BACKBONE:
    DILATION: False
    FREEZE: False
    NAME: resnet101
    POS_ENC: sine
  WEIGHT: data/hc-stvg2/checkpoints/hcstvg2.pth
OUTPUT_DIR: output/online_hcstvg2
SOLVER:
  ACTIONESS_COEF: 2
  ATTN_COEF: 1
  BASE_LR: 0.0003
  BATCH_SIZE: 1
  BBOX_COEF: 5
  CHECKPOINT_PERIOD: 2000
  CONF_COEF: 1
  EOS_COEF: 0.3
  GAMMA: 0.1
  GIOU_COEF: 4
  MAX_EPOCH: 90
  MAX_GRAD_NORM: 0.1
  MOMENTUM: 0.9
  OPTIMIZER: adamw
  POWER: 0.9
  PRE_VAL: False
  SCHEDULE:
    COOLDOWN: 1
    DROP_STEP: [50, 90]
    FACTOR: 0.5
    MAX_DECAY_STEP: 7
    PATIENCE: 2
    THRESHOLD: 0.0001
    TYPE: multistep_with_warmup
  SHUFFLE: True
  SIGMA: 2.0
  STEPS: (30000,)
  TEMP_COEF: 10
  TEMP_LR: 0.0001
  TEXT_LR: 5e-05
  TO_VAL: True
  USE_ATTN: False
  USE_AUX_LOSS: True
  VAL_PERIOD: 500
  VIS_BACKBONE_LR: 2e-05
  WARMUP_FACTOR: 0.3333333333333333
  WARMUP_ITERS: 500
  WARMUP_METHOD: linear
  WARMUP_PROP: 0.01
  WEIGHT_DECAY: 0.0001
TENSORBOARD_DIR: data/hc-stvg2/checkpoints/
load from model_zoo/swin_tiny_patch244_window877_kinetics400_1k.pth.
load from model_zoo/swin_tiny_patch244_window877_kinetics400_1k.pth.
load from model_zoo/swin_tiny_patch244_window877_kinetics400_1k.pth.
load from model_zoo/swin_tiny_patch244_window877_kinetics400_1k.pth.
2025-09-09 18:05:26,958 Video Grounding INFO: Loading checkpoint from data/hc-stvg2/checkpoints/hcstvg2.pth
原始数据集大小: 1902
验证数据集大小: 10
为 test 集创建固定大小滑动窗口...
窗口大小: 8 帧, 滑动步长: 1 帧

  0%|          | 0/10 [00:00<?, ?it/s]
100%|██████████| 10/10 [00:00<00:00, 10897.13it/s]
成功生成 5275 个滑动窗口。
原始数据集大小: 1902
验证数据集大小: 10
为 test 集创建固定大小滑动窗口...
窗口大小: 8 帧, 滑动步长: 1 帧

  0%|          | 0/10 [00:00<?, ?it/s]
100%|██████████| 10/10 [00:00<00:00, 10661.68it/s]
成功生成 5275 个滑动窗口。
原始数据集大小: 1902
验证数据集大小: 10
为 test 集创建固定大小滑动窗口...
窗口大小: 8 帧, 滑动步长: 1 帧

  0%|          | 0/10 [00:00<?, ?it/s]
100%|██████████| 10/10 [00:00<00:00, 11208.72it/s]
成功生成 5275 个滑动窗口。
GPU 1: 分配 3 个视频（[3, 4, 5]）, 1699 个窗口
原始数据集大小: 1902
验证数据集大小: 10
为 test 集创建固定大小滑动窗口...
窗口大小: 8 帧, 滑动步长: 1 帧

  0%|          | 0/10 [00:00<?, ?it/s]
100%|██████████| 10/10 [00:00<00:00, 10959.77it/s]
成功生成 5275 个滑动窗口。
原始数据集大小: 1902
验证数据集大小: 10
为 test 集创建固定大小滑动窗口...
窗口大小: 8 帧, 滑动步长: 1 帧

  0%|          | 0/10 [00:00<?, ?it/s]
100%|██████████| 10/10 [00:00<00:00, 10637.34it/s]
成功生成 5275 个滑动窗口。
原始数据集大小: 1902
验证数据集大小: 10
为 test 集创建固定大小滑动窗口...
窗口大小: 8 帧, 滑动步长: 1 帧

  0%|          | 0/10 [00:00<?, ?it/s]
100%|██████████| 10/10 [00:00<00:00, 7851.56it/s]
成功生成 5275 个滑动窗口。
GPU 2: 分配 3 个视频（[6, 7, 8]）, 1579 个窗口
原始数据集大小: 1902
原始数据集大小: 1902
验证数据集大小: 10
为 test 集创建固定大小滑动窗口...
验证数据集大小: 10窗口大小: 8 帧, 滑动步长: 1 帧

为 test 集创建固定大小滑动窗口...
窗口大小: 8 帧, 滑动步长: 1 帧

  0%|          | 0/10 [00:00<?, ?it/s]
  0%|          | 0/10 [00:00<?, ?it/s]
100%|██████████| 10/10 [00:00<00:00, 9718.04it/s]
成功生成 5275 个滑动窗口。

100%|██████████| 10/10 [00:00<00:00, 9549.87it/s]
成功生成 5275 个滑动窗口。
GPU 3: 分配 1 个视频（[9]）, 499 个窗口
GPU 0: 分配 3 个视频（[0, 1, 2]）, 1498 个窗口
[rank1]: Traceback (most recent call last):
[rank1]:   File "/data/xiaowenhui/CGSTVG-main/scripts/test_net.py", line 125, in <module>
[rank1]:     main()
[rank1]:   File "/data/xiaowenhui/CGSTVG-main/scripts/test_net.py", line 98, in main
[rank1]:     do_online_eval(
[rank1]:   File "/home/<USER>/anaconda3/envs/CGSTVG/lib/python3.9/site-packages/torch/utils/_contextlib.py", line 115, in decorate_context
[rank1]:     return func(*args, **kwargs)
[rank1]:   File "/data/xiaowenhui/CGSTVG-main/engine/online_evaluate.py", line 176, in do_online_eval
[rank1]:     video_progress = create_logging_tqdm(total=video_total, desc="处理视频", logger=logger) if video_total is not None else None
[rank1]: TypeError: create_logging_tqdm() missing 1 required positional argument: 'iterable'
[rank2]: Traceback (most recent call last):
[rank2]:   File "/data/xiaowenhui/CGSTVG-main/scripts/test_net.py", line 125, in <module>
[rank2]:     main()
[rank2]:   File "/data/xiaowenhui/CGSTVG-main/scripts/test_net.py", line 98, in main
[rank2]:     do_online_eval(
[rank2]:   File "/home/<USER>/anaconda3/envs/CGSTVG/lib/python3.9/site-packages/torch/utils/_contextlib.py", line 115, in decorate_context
[rank2]:     return func(*args, **kwargs)
[rank2]:   File "/data/xiaowenhui/CGSTVG-main/engine/online_evaluate.py", line 176, in do_online_eval
[rank2]:     video_progress = create_logging_tqdm(total=video_total, desc="处理视频", logger=logger) if video_total is not None else None
[rank2]: TypeError: create_logging_tqdm() missing 1 required positional argument: 'iterable'
[rank3]: Traceback (most recent call last):
[rank3]:   File "/data/xiaowenhui/CGSTVG-main/scripts/test_net.py", line 125, in <module>
[rank3]:     main()
[rank3]:   File "/data/xiaowenhui/CGSTVG-main/scripts/test_net.py", line 98, in main
[rank3]:     do_online_eval(
[rank3]:   File "/home/<USER>/anaconda3/envs/CGSTVG/lib/python3.9/site-packages/torch/utils/_contextlib.py", line 115, in decorate_context
[rank3]:     return func(*args, **kwargs)
[rank3]:   File "/data/xiaowenhui/CGSTVG-main/engine/online_evaluate.py", line 176, in do_online_eval
[rank3]:     video_progress = create_logging_tqdm(total=video_total, desc="处理视频", logger=logger) if video_total is not None else None
[rank3]: TypeError: create_logging_tqdm() missing 1 required positional argument: 'iterable'
2025-09-09 18:05:30,928 Video Grounding INFO: Start Online Testing...
2025-09-09 18:05:30,932 Video Grounding INFO: Start online datasets evaluation on the test split of Online-HC-STVG dataset
2025-09-09 18:05:30,932 Video Grounding INFO: 🎯 开始在线评估 - 设备: cuda, 数据集: Online-HC-STVG
[rank0]: Traceback (most recent call last):
[rank0]:   File "/data/xiaowenhui/CGSTVG-main/scripts/test_net.py", line 125, in <module>
[rank0]:     main()
[rank0]:   File "/data/xiaowenhui/CGSTVG-main/scripts/test_net.py", line 98, in main
[rank0]:     do_online_eval(
[rank0]:   File "/home/<USER>/anaconda3/envs/CGSTVG/lib/python3.9/site-packages/torch/utils/_contextlib.py", line 115, in decorate_context
[rank0]:     return func(*args, **kwargs)
[rank0]:   File "/data/xiaowenhui/CGSTVG-main/engine/online_evaluate.py", line 176, in do_online_eval
[rank0]:     video_progress = create_logging_tqdm(total=video_total, desc="处理视频", logger=logger) if video_total is not None else None
[rank0]: TypeError: create_logging_tqdm() missing 1 required positional argument: 'iterable'
E0909 18:05:36.550390 136905945012032 torch/distributed/elastic/multiprocessing/api.py:826] failed (exitcode: 1) local_rank: 0 (pid: 2572466) of binary: /home/<USER>/anaconda3/envs/CGSTVG/bin/python
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/CGSTVG/bin/torchrun", line 33, in <module>
    sys.exit(load_entry_point('torch==2.3.0', 'console_scripts', 'torchrun')())
  File "/home/<USER>/anaconda3/envs/CGSTVG/lib/python3.9/site-packages/torch/distributed/elastic/multiprocessing/errors/__init__.py", line 347, in wrapper
    return f(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/CGSTVG/lib/python3.9/site-packages/torch/distributed/run.py", line 879, in main
    run(args)
  File "/home/<USER>/anaconda3/envs/CGSTVG/lib/python3.9/site-packages/torch/distributed/run.py", line 870, in run
    elastic_launch(
  File "/home/<USER>/anaconda3/envs/CGSTVG/lib/python3.9/site-packages/torch/distributed/launcher/api.py", line 132, in __call__
    return launch_agent(self._config, self._entrypoint, list(args))
  File "/home/<USER>/anaconda3/envs/CGSTVG/lib/python3.9/site-packages/torch/distributed/launcher/api.py", line 263, in launch_agent
    raise ChildFailedError(
torch.distributed.elastic.multiprocessing.errors.ChildFailedError: 
============================================================
scripts/test_net.py FAILED
------------------------------------------------------------
Failures:
[1]:
  time      : 2025-09-09_18:05:36
  host      : eipc31
  rank      : 1 (local_rank: 1)
  exitcode  : 1 (pid: 2572469)
  error_file: <N/A>
  traceback : To enable traceback see: https://pytorch.org/docs/stable/elastic/errors.html
[2]:
  time      : 2025-09-09_18:05:36
  host      : eipc31
  rank      : 2 (local_rank: 2)
  exitcode  : 1 (pid: 2572475)
  error_file: <N/A>
  traceback : To enable traceback see: https://pytorch.org/docs/stable/elastic/errors.html
[3]:
  time      : 2025-09-09_18:05:36
  host      : eipc31
  rank      : 3 (local_rank: 3)
  exitcode  : 1 (pid: 2572479)
  error_file: <N/A>
  traceback : To enable traceback see: https://pytorch.org/docs/stable/elastic/errors.html
------------------------------------------------------------
Root Cause (first observed failure):
[0]:
  time      : 2025-09-09_18:05:36
  host      : eipc31
  rank      : 0 (local_rank: 0)
  exitcode  : 1 (pid: 2572466)
  error_file: <N/A>
  traceback : To enable traceback see: https://pytorch.org/docs/stable/elastic/errors.html
============================================================
