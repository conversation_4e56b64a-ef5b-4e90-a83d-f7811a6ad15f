import os
import torch
import argparse
import json
from utils.comm import get_rank, get_world_size, all_gather, synchronize

# Minimal entry to init distributed and build sampler
def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--config-file', type=str, default='experiments/online_hcstvg2.yaml')
    parser.add_argument('--seed', type=int, default=None, help='Optional seed used before shuffling')
    args = parser.parse_args()

    # init dist
    if 'LOCAL_RANK' in os.environ:
        local_rank = int(os.environ['LOCAL_RANK'])
        torch.cuda.set_device(local_rank)
        torch.distributed.init_process_group(backend='nccl', init_method='env://')

    from config import cfg
    cfg.merge_from_file(args.config_file)
    cfg.freeze()

    # set seed if provided
    if args.seed is not None:
        torch.manual_seed(args.seed)

    # lazy import dataset builder
    from datasets import build_dataset
    from datasets.build import VideoGroupedSampler

    dataset = build_dataset(cfg, 'test', transforms=None)
    # Use same shuffle flag as in make_data_loader for test mode (False)
    sampler = VideoGroupedSampler(dataset, shuffle=False)

    # collect assigned video ids
    assigned_video_ids = sorted(list({dataset.window_infos[i]['anno_idx'] for i in sampler.indices}))

    # gather from all ranks
    gathered = all_gather(assigned_video_ids)
    synchronize()

    if get_rank() == 0:
        print('--- Assigned video IDs per rank (full lists) ---')
        for r, vids in enumerate(gathered):
            print(f'Rank {r}: {len(vids)} videos')
            print(json.dumps(vids, ensure_ascii=False))

        # check overlaps
        flat = []
        for vids in gathered:
            flat.extend(vids)
        dup = set([x for x in flat if flat.count(x) > 1])
        if len(dup) == 0:
            print('No overlaps between ranks: OK')
        else:
            print(f'Found overlapping video ids across ranks: {sorted(list(dup))}')


if __name__ == '__main__':
    main()
