#!/usr/bin/env bash
# Run HC-STVG2 experiments on specified GPUs (train or test)
# Usage examples:
#  ./scripts/run_hcstvg2.sh --gpus 0,2,5 --mode train
#  ./scripts/run_hcstvg2.sh --gpus 0,1 --mode test --weights /path/to/ckpt.pth

set -euo pipefail

# Ensure repository root is on PYTHONPATH so modules like `config` can be imported
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
REPO_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
export PYTHONPATH="$REPO_ROOT${PYTHONPATH:+:$PYTHONPATH}"

# Ensure the torch shared libs are visible to the dynamic loader. Find torch lib dir via python.
TORCH_LIB_DIR=$(python - <<'PY'
import os, torch
print(os.path.join(os.path.dirname(torch.__file__), 'lib'))
PY
)
if [[ -d "$TORCH_LIB_DIR" ]]; then
  export LD_LIBRARY_PATH="$TORCH_LIB_DIR${LD_LIBRARY_PATH:+:$LD_LIBRARY_PATH}"
fi


print_help() {
  cat <<-EOF
Usage: $0 [options]

Options:
  --gpus G1,G2,...     Comma-separated physical GPU ids to use (e.g. 0,2,5). If omitted, try to auto-detect all GPUs.
  --mode train|test    Mode to run (default: train)
  --weights PATH       Required for test mode: path to checkpoint
  --nproc N            Number of processes per node (defaults to number of GPUs provided)
  --config PATH        Config file (default: experiments/hcstvg2.yaml)
  --resolution N       INPUT.RESOLUTION (default: 420)
  --output DIR         OUTPUT_DIR (default: output/hcstvg2)
  --tbdir DIR          TENSORBOARD_DIR (default: output/hcstvg2)
  -h, --help           Show this help

Examples:
  # Train on physical GPUs 0,2,5
  $0 --gpus 0,2,5 --mode train

  # Test on GPUs 0 and 1
  $0 --gpus 0,1 --mode test --weights /path/to/ckpt.pth
EOF
}

# defaults
GPUS=""
MODE="train"
WEIGHTS="data/hc-stvg2/checkpoints/hcstvg2.pth"
NPROC=""
CONFIG="experiments/online_hcstvg2.yaml"
RESOLUTION=420
OUTPUT_DIR="output/online_hcstvg2"
TB_DIR="output/online_hcstvg2"

while [[ $# -gt 0 ]]; do
  case "$1" in
    --gpus)
      GPUS="$2"; shift 2;;
    --mode)
      MODE="$2"; shift 2;;
    --weights)
      WEIGHTS="$2"; shift 2;;
    --nproc)
      NPROC="$2"; shift 2;;
    --config)
      CONFIG="$2"; shift 2;;
    --resolution)
      RESOLUTION="$2"; shift 2;;
    --output)
      OUTPUT_DIR="$2"; shift 2;;
    --tbdir)
      TB_DIR="$2"; shift 2;;
    -h|--help)
      print_help; exit 0;;
    --)
      shift; break;;
    *)
      echo "Unknown option: $1"; print_help; exit 1;;
  esac
done

# Auto-detect GPUs if none provided
if [[ -z "${GPUS}" ]]; then
  if [[ -n "${CUDA_VISIBLE_DEVICES:-}" ]]; then
    GPUS="$CUDA_VISIBLE_DEVICES"
  else
    if command -v nvidia-smi >/dev/null 2>&1; then
      NGPU=$(nvidia-smi --query-gpu=count --format=csv,noheader,nounits 2>/dev/null || true)
      if [[ -n "$NGPU" && "$NGPU" -gt 0 ]]; then
        # build comma list 0,1,..,NGPU-1
        if [[ "$NGPU" -eq 1 ]]; then
          GPUS="0"
        else
          GPUS=$(seq -s, 0 $((NGPU-1)))
        fi
      fi
    fi
  fi
fi

if [[ -z "${GPUS}" ]]; then
  echo "No GPUs found or specified. Set --gpus or ensure nvidia-smi is available." >&2
  exit 2
fi

# compute nproc if not provided
if [[ -z "${NPROC}" ]]; then
  NPROC=$(echo "$GPUS" | tr ',' '\n' | sed '/^$/d' | wc -l | tr -d ' ')
fi

if [[ "$NPROC" -le 0 ]]; then
  echo "Invalid number of processes: $NPROC" >&2
  exit 3
fi

export CUDA_VISIBLE_DEVICES="$GPUS"

echo "Running HC-STVG2"
echo "  MODE: $MODE"
echo "  GPUs (physical ids): $GPUS"
echo "  nproc_per_node: $NPROC"
echo "  CONFIG: $CONFIG"
echo "  RESOLUTION: $RESOLUTION"
echo "  OUTPUT_DIR: $OUTPUT_DIR"

# 在启动训练/测试前添加调试信息
echo "=== GPU分配调试信息 ==="
echo "CUDA_VISIBLE_DEVICES: $CUDA_VISIBLE_DEVICES"
echo "GPUS: $GPUS"
echo "NPROC: $NPROC"

# 检查每个进程看到的GPU
python -c "
import torch
import os
print(f'CUDA_VISIBLE_DEVICES: {os.environ.get(\"CUDA_VISIBLE_DEVICES\", \"Not set\")}')
print(f'torch.cuda.device_count(): {torch.cuda.device_count()}')
for i in range(torch.cuda.device_count()):
    print(f'GPU {i}: {torch.cuda.get_device_name(i)}')
"

if [[ "$MODE" == "test" ]]; then
  echo "Launching testing..."
  exec torchrun --nproc_per_node=$NPROC \
    --master_port=29500 \
    scripts/test_net.py \
    --config-file "$CONFIG" \
    INPUT.RESOLUTION $RESOLUTION \
    OUTPUT_DIR "$OUTPUT_DIR" \
    MODEL.WEIGHT "$WEIGHTS"
else
  echo "Unknown mode: $MODE" >&2
  exit 5
fi