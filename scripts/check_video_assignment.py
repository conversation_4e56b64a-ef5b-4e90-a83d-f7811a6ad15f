import os
import torch
import argparse
from datasets import build_dataset, make_data_loader
from datasets.build import VideoGroupedSampler
from utils.comm import get_rank, get_world_size

import yaml


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--config-file', type=str, default='experiments/online_hcstvg2.yaml')
    args = parser.parse_args()

    # minimal config parsing: just import config
    from config import cfg
    cfg.merge_from_file(args.config_file)
    cfg.freeze()

    # build transforms and dataset
    transforms = None
    dataset = build_dataset(cfg, 'test', transforms)

    # construct sampler
    sampler = VideoGroupedSampler(dataset, shuffle=False)

    # compute assigned video ids
    assigned_video_ids = set()
    for idx in sampler.indices:
        assigned_video_ids.add(dataset.window_infos[idx]['anno_idx'])

    print(f"Rank {get_rank()} / {get_world_size()} assigned videos: {sorted(list(assigned_video_ids))}")

if __name__ == '__main__':
    main()
