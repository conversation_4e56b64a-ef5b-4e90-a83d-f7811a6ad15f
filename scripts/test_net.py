import argparse
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import torch
import torch.backends.cudnn as cudnn

from config import cfg
from utils.comm import synchronize, get_rank
from utils.logger import setup_logger
from utils.misc import mkdir, set_seed
from utils.checkpoint import VSTGCheckpointer
from datasets import make_data_loader, build_evaluator, build_dataset
from models import build_model, build_postprocessors
from engine import do_eval
from engine.online_evaluate import do_online_eval # 导入新的在线评估函数

def main():
    args = default_argument_parser().parse_args()
    
    # 添加GPU分配验证
    import os
    local_rank = int(os.environ.get('LOCAL_RANK', 0))
    world_size = int(os.environ.get('WORLD_SIZE', 1))
    
    print(f"=== 进程 {local_rank}/{world_size} GPU分配信息 ===")
    print(f"CUDA_VISIBLE_DEVICES: {os.environ.get('CUDA_VISIBLE_DEVICES', 'Not set')}")
    print(f"LOCAL_RANK: {local_rank}")
    print(f"torch.cuda.device_count(): {torch.cuda.device_count()}")
    print(f"torch.cuda.current_device(): {torch.cuda.current_device()}")
    
    if torch.cuda.is_available():
        device_name = torch.cuda.get_device_name(torch.cuda.current_device())
        print(f"当前使用GPU: {device_name}")
        
        # 测试GPU内存分配
        test_tensor = torch.randn(100, 100).cuda()
        print(f"GPU内存测试成功，张量设备: {test_tensor.device}")
    
    cfg = setup(args)
    
    # 确保每个进程使用正确的GPU
    if torch.cuda.is_available():
        torch.cuda.set_device(local_rank)
        device = torch.device(f"cuda:{local_rank}")
    else:
        device = torch.device("cpu")
    
    print(f"进程 {local_rank} 最终使用设备: {device}")
    
    if args.distributed:
        torch.cuda.set_device(args.local_rank)
        torch.distributed.init_process_group(
            backend="nccl", init_method="env://"
        )
        synchronize()

    if args.config_file:
        cfg.merge_from_file(args.config_file)
        
    cfg.merge_from_list(args.opts)
    cfg.freeze()

    if args.use_seed:
        cudnn.benchmark = False
        cudnn.deterministic = True
        set_seed(args.seed + get_rank())

    output_dir = cfg.OUTPUT_DIR
    if output_dir:
        mkdir(output_dir)

    logger = setup_logger("Video Grounding", output_dir, get_rank())
    logger.info("Using {} GPUs".format(num_gpus))
    logger.info(cfg)
    os.environ["TOKENIZERS_PARALLELISM"] = "false"
    
    model, _, _ = build_model(cfg)
    device = torch.device(cfg.MODEL.DEVICE)
    model.to(device)
    
    checkpointer = VSTGCheckpointer(cfg, model, logger=logger, is_train=False)
    _ = checkpointer.load(cfg.MODEL.WEIGHT, with_optim=False)
    
    # Prepare the dataset cache
    if args.local_rank == 0:
        _ = build_dataset(cfg, split='test', transforms=None)
        
    synchronize()
    
    test_data_loader = make_data_loader(
        cfg,
        mode='test',
        is_distributed=args.distributed,
    )
    
    # Build evaluator and postprocessor for both online and offline evaluation
    evaluator = build_evaluator(cfg, logger, mode='test')  # mode = ['val','test']
    postprocessor = build_postprocessors()
    
    if cfg.DATASET.NAME == "Online-HC-STVG":
        logger.info("Start Online Testing...")
        gpu_results = do_online_eval(
            cfg, "test", logger, model, postprocessor, test_data_loader, evaluator, device
        )
        
        # 每个GPU独立输出自己的结果
        if gpu_results is not None:
            rank = get_rank()
            logger.info(f"GPU {rank} 最终结果:")
            for key, value in gpu_results.items():
                if isinstance(value, (int, float)):
                    logger.info(f"  {key}: {value:.4f}")
                else:
                    logger.info(f"  {key}: {value}")
    else:
        logger.info("Start Testing")
        # Keep the original offline evaluation logic
        do_eval(
            cfg,
            mode='test',
            logger=logger,
            model=model,
            postprocessor=postprocessor,
            data_loader=test_data_loader,
            evaluator=evaluator,
            device=device
        )
        synchronize()


if __name__ == "__main__":
    main()
