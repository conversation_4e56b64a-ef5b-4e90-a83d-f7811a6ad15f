#!/usr/bin/env bash
# Test multi-GPU setup for OnlineHCSTVGDataset
# Usage: ./scripts/test_multigpu_setup.sh --gpus 0,1,2,3

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
REPO_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
export PYTHONPATH="$REPO_ROOT${PYTHONPATH:+:$PYTHONPATH}"

# Parse arguments
GPUS=""
while [[ $# -gt 0 ]]; do
  case "$1" in
    --gpus)
      GPUS="$2"; shift 2;;
    -h|--help)
      echo "Usage: $0 --gpus G1,G2,... (e.g., --gpus 0,1,2,3)"
      exit 0;;
    *)
      echo "Unknown option: $1"; exit 1;;
  esac
done

if [[ -z "${GPUS}" ]]; then
  echo "Error: --gpus is required"
  exit 1
fi

export CUDA_VISIBLE_DEVICES="$GPUS"
NPROC=$(echo "$GPUS" | tr ',' '\n' | sed '/^$/d' | wc -l | tr -d ' ')

echo "Testing multi-GPU setup with GPUs: $GPUS (nproc: $NPROC)"

# Create a simple test script
cat > /tmp/test_multigpu.py << 'EOF'
import os
import torch
import torch.distributed as dist
from utils.comm import get_rank, get_world_size

def test_multigpu():
    # Initialize distributed training
    if 'LOCAL_RANK' in os.environ:
        local_rank = int(os.environ['LOCAL_RANK'])
        torch.cuda.set_device(local_rank)
        dist.init_process_group(backend='nccl', init_method='env://')
    
    rank = get_rank()
    world_size = get_world_size()
    
    print(f"Process {rank}/{world_size}: CUDA_VISIBLE_DEVICES={os.environ.get('CUDA_VISIBLE_DEVICES')}")
    print(f"Process {rank}/{world_size}: Using GPU {torch.cuda.current_device()}")
    print(f"Process {rank}/{world_size}: GPU name: {torch.cuda.get_device_name()}")
    
    # Test tensor operations
    device = torch.device(f'cuda:{local_rank}' if 'LOCAL_RANK' in os.environ else 'cuda')
    x = torch.randn(10, 10).to(device)
    y = x @ x.t()
    print(f"Process {rank}/{world_size}: Tensor operation successful, result shape: {y.shape}")
    
    if dist.is_initialized():
        dist.barrier()
        print(f"Process {rank}/{world_size}: Synchronization successful")

if __name__ == "__main__":
    test_multigpu()
EOF

# Run the test
torchrun --nproc_per_node=$NPROC /tmp/test_multigpu.py

echo "Multi-GPU test completed successfully!"
